{"name": "popz-place-radio", "version": "1.1.0", "description": "A web-based music player connected to Azure Storage", "main": "script.js", "type": "module", "scripts": {"start": "node server.js", "build": "vite build", "serve": "vite preview"}, "dependencies": {"@azure/storage-blob": "^12.27.0", "dotenv": "^16.3.1", "express": "^5.1.0", "music-metadata-browser": "^2.5.10"}, "devDependencies": {"vite": "^4.4.9"}}