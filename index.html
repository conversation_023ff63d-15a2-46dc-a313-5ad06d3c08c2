<!DOCTYPE html>
<html>
<head>
    <title>Popz Place Radio</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#bc13fe">
    <meta name="screen-orientation" content="landscape">
    <link href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@700&family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #1a1a1a;
            color: white;
            font-family: 'Roboto', sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .banner {
            margin-bottom: 40px;
            text-align: center;
        }

        .neon-text {
            font-family: 'Dancing Script', cursive;
            font-size: 4em;
            color: #fff;
            text-shadow: 0 0 7px #fff,
                       0 0 10px #fff,
                       0 0 21px #fff,
                       0 0 42px #bc13fe,
                       0 0 82px #bc13fe,
                       0 0 92px #bc13fe,
                       0 0 102px #bc13fe,
                       0 0 151px #bc13fe;
            animation: neon 1.5s ease-in-out infinite alternate;
        }

        @keyframes neon {
            from {
                text-shadow: 0 0 7px #fff,
                             0 0 10px #fff,
                             0 0 21px #fff,
                             0 0 42px #bc13fe,
                             0 0 82px #bc13fe,
                             0 0 92px #bc13fe,
                             0 0 102px #bc13fe,
                             0 0 151px #bc13fe;
            }
            to {
                text-shadow: 0 0 7px #fff,
                             0 0 10px #fff,
                             0 0 21px #fff,
                             0 0 42px #f09,
                             0 0 82px #f09,
                             0 0 92px #f09,
                             0 0 102px #f09,
                             0 0 151px #f09;
            }
        }

        .player {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 0 20px rgba(188, 19, 254, 0.2);
            backdrop-filter: blur(10px);
            width: 85%;
            max-width: 550px;
            position: relative;
        }

        .controls {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin: 20px 0;
            max-width: 100%;
        }

        .controls-row-2 {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 10px 0;
            max-width: 100%;
        }

        .volume-control {
            grid-column: span 4;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-top: 10px;
        }

        .control-button {
            background: none;
            border: 2px solid #bc13fe;
            color: #fff;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .control-button:hover {
            transform: scale(1.1);
            text-shadow: 0 0 10px rgba(188, 19, 254, 0.8);
        }

        .control-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .progress {
            width: 100%;
            height: 5px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            margin: 20px 0;
            cursor: pointer;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #bc13fe, #f09);
            border-radius: 5px;
            width: 0%;
            transition: width 0.1s linear;
        }

        #status {
            margin-top: 20px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9em;
        }

        .favorites-modal {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(188, 19, 254, 0.3);
            z-index: 1000;
            max-width: 80%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .favorites-modal h2 {
            color: #fff;
            margin-top: 0;
            font-size: 1.5em;
            border-bottom: 1px solid rgba(188, 19, 254, 0.3);
            padding-bottom: 10px;
        }

        .favorites-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .favorites-list li {
            color: #fff;
            padding: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .favorites-list li:hover {
            background: rgba(188, 19, 254, 0.1);
        }

        .favorites-list button {
            background: none;
            border: none;
            color: #ff4444;
            cursor: pointer;
            padding: 5px;
        }

        .favorites-modal .close-button,
        .metadata-modal .close-button {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            color: #fff;
            cursor: pointer;
            font-size: 1.2em;
        }

        .metadata-modal {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(188, 19, 254, 0.3);
            z-index: 1000;
            min-width: 300px;
        }

        .metadata-modal h2 {
            color: #fff;
            margin-top: 0;
            font-size: 1.5em;
            border-bottom: 1px solid rgba(188, 19, 254, 0.3);
            padding-bottom: 10px;
            margin-bottom: 20px;
        }

        .metadata-row {
            display: flex;
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .metadata-label {
            color: rgba(255, 255, 255, 0.7);
            width: 100px;
            flex-shrink: 0;
        }

        .metadata-value {
            color: #fff;
            flex-grow: 1;
        }

        .control-button i.fas.fa-heart {
            color: #ff4444;
        }

        #player {
            display: none;
        }

        .tracks-modal {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(26, 26, 26, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(188, 19, 254, 0.4);
            z-index: 1000;
            max-height: 80vh;
            overflow-y: auto;
            min-width: 300px;
            backdrop-filter: blur(10px);
        }

        .tracks-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .track-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .track-item:hover {
            background: rgba(188, 19, 254, 0.2);
        }

        .track-item.current {
            border-left: 3px solid #bc13fe;
            background: rgba(188, 19, 254, 0.15);
        }

        /* Volume slider styling */
        .volume-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .volume-slider-container {
            width: 100px;
        }

        #volume-slider {
            width: 100%;
            height: 5px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            outline: none;
            -webkit-appearance: none;
        }

        #volume-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 15px;
            height: 15px;
            background: #bc13fe;
            border-radius: 50%;
            cursor: pointer;
        }

        #volume-slider::-moz-range-thumb {
            width: 15px;
            height: 15px;
            background: #bc13fe;
            border-radius: 50%;
            cursor: pointer;
            border: none;
        }

        /* Samsung Z Fold3 optimizations */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .neon-text {
                font-size: 2.2em;
                margin-bottom: 10px;
            }

            .banner {
                margin-bottom: 20px;
            }

            .player {
                width: 98%;
                padding: 15px;
                max-width: none;
            }

            .controls {
                grid-template-columns: repeat(4, 1fr);
                gap: 10px;
                margin: 15px 0 10px 0;
            }

            .controls-row-2 {
                grid-template-columns: repeat(3, 1fr);
                gap: 10px;
                margin: 5px 0;
            }

            .control-button {
                width: 40px;
                height: 40px;
                font-size: 0.85em;
                min-width: 40px;
                min-height: 40px;
            }

            .volume-control {
                grid-column: span 4;
                margin-top: 5px;
            }

            .volume-slider-container {
                width: 120px;
            }

            #status {
                font-size: 0.9em;
                margin-top: 10px;
            }
        }

        /* Z Fold3 unfolded (landscape tablet mode) */
        @media (min-width: 769px) and (max-width: 1024px) {
            .player {
                width: 90%;
                max-width: 700px;
            }

            .controls {
                grid-template-columns: repeat(6, 1fr);
                gap: 20px;
            }

            .controls-row-2 {
                grid-template-columns: repeat(4, 1fr);
                gap: 20px;
            }

            .volume-control {
                grid-column: span 6;
            }
        }

        /* Force landscape orientation */
        @media (orientation: portrait) and (max-width: 768px) {
            body::before {
                content: "Please rotate your device to landscape mode for the best experience";
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.9);
                color: white;
                padding: 20px;
                border-radius: 10px;
                text-align: center;
                z-index: 9999;
                font-size: 1.1em;
            }

            .banner, .player {
                filter: blur(3px);
                pointer-events: none;
            }
        }

        /* Prevent text selection on mobile */
        .player, .controls, .control-button {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-touch-callout: none;
        }

        /* Improve touch targets */
        .control-button {
            min-width: 44px;
            min-height: 44px;
        }


    </style>
</head>
<body>
    <div class="banner">
        <h1 class="neon-text">Popz Place Radio</h1>
    </div>

    <div class="player">
        <div class="progress">
            <div class="progress-bar" id="progress-bar"></div>
        </div>

        <!-- Main playback controls (4 buttons per row) -->
        <div class="controls">
            <button class="control-button" id="prev-button" title="Previous">
                <i class="fas fa-backward"></i>
            </button>
            <button class="control-button" id="play-button" title="Play/Pause">
                <i class="fas fa-play"></i>
            </button>
            <button class="control-button" id="next-button" title="Next">
                <i class="fas fa-forward"></i>
            </button>
            <button class="control-button" id="stop-button" title="Stop">
                <i class="fas fa-stop"></i>
            </button>
        </div>

        <!-- Secondary controls (3 buttons per row) -->
        <div class="controls-row-2">
            <button class="control-button" id="shuffle-button" title="Shuffle">
                <i class="fas fa-random"></i>
            </button>
            <button class="control-button" id="favorite-button" title="Toggle Favorite">
                <i class="far fa-heart"></i>
            </button>
            <button class="control-button" id="show-tracks-button" title="Show All Tracks">
                <i class="fas fa-music"></i>
            </button>
        </div>

        <!-- Tertiary controls (3 buttons per row) -->
        <div class="controls-row-2">
            <button class="control-button" id="favorites-list-button" title="Show Favorites">
                <i class="fas fa-list"></i>
            </button>
            <button class="control-button" id="play-favorites-button" title="Play Favorites Only">
                <i class="fas fa-star"></i>
            </button>
            <button class="control-button" id="version-button" title="Show Version">
                <i class="fas fa-info-circle"></i>
            </button>
        </div>

        <!-- Volume control (full width) -->
        <div class="volume-control">
            <button class="control-button" id="volume-button" title="Toggle Mute">
                <i class="fas fa-volume-up"></i>
            </button>
            <div class="volume-slider-container">
                <input type="range" id="volume-slider" min="0" max="100" value="50">
            </div>
        </div>

        <div id="status">Click play to start</div>
    </div>

    <div class="favorites-modal" id="favorites-modal">
        <button class="close-button" id="close-favorites">
            <i class="fas fa-times"></i>
        </button>
        <h2>Favorite Tracks</h2>
        <ul class="favorites-list" id="favorites-list"></ul>
    </div>

    <div class="tracks-modal" id="tracks-modal">
        <button class="close-button" id="close-tracks">
            <i class="fas fa-times"></i>
        </button>
        <h2>All Tracks</h2>
        <ul class="tracks-list" id="tracks-list"></ul>
    </div>

    <audio id="player"></audio>
    <script type="module" src="/player.js"></script>
</body>
</html>
