# Popz Place Radio - Environment Configuration Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# AZURE STORAGE CONFIGURATION
# =============================================================================

# Azure Storage Connection String
# Get this from Azure Portal > Storage Account > Access Keys
VITE_AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=YOUR_ACCOUNT_NAME;AccountKey=YOUR_ACCOUNT_KEY;EndpointSuffix=core.windows.net

# Azure Storage Access Key
# Primary or Secondary key from Azure Portal > Storage Account > Access Keys
VITE_AZURE_STORAGE_ACCESS_KEY=your_storage_access_key_here

# Azure Storage Account Name
# The name of your Azure Storage Account
VITE_AZURE_STORAGE_ACCOUNT=your_storage_account_name

# Azure Storage Container Name
# The blob container where your music files are stored
VITE_AZURE_STORAGE_CONTAINER_NAME=music

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Server Port
# Port number for the Express server (default: 3000)
PORT=3000

# Node Environment
# Set to 'development' for local development, 'production' for deployment
NODE_ENV=development

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================

# Application Insights Connection String (optional)
# For monitoring and analytics in Azure
APPLICATIONINSIGHTS_CONNECTION_STRING=InstrumentationKey=your_key_here

# Custom Domain (optional)
# If using a custom domain for the application
CUSTOM_DOMAIN=your-domain.com

# Debug Mode (optional)
# Set to 'true' to enable debug logging
DEBUG=false

# =============================================================================
# EXAMPLE VALUES (DO NOT USE IN PRODUCTION)
# =============================================================================

# Example connection string format:
# VITE_AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=popzplaceradio;AccountKey=abcd1234...;EndpointSuffix=core.windows.net

# Example access key format:
# VITE_AZURE_STORAGE_ACCESS_KEY=abcd1234efgh5678ijkl9012mnop3456qrst7890uvwx1234yzab5678cdef9012==

# Example account name:
# VITE_AZURE_STORAGE_ACCOUNT=popzplaceradio

# Example container name:
# VITE_AZURE_STORAGE_CONTAINER_NAME=music
